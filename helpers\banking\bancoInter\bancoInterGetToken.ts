import 'server-only'

import { action } from '@/lib/actions/action'
import { kv } from '@vercel/kv'
import fetch from 'node-fetch'
import { z } from 'zod'

import { getHttpsAgentSignedWithCertAndKey } from '@/helpers/certificate/getHttpsAgentSignedWithCertAndKey'

const schema = z.object({
	access_token: z.string().transform(token => {
		if (!token.startsWith('Bearer')) token = `Bearer ${token}`
		return token
	}),
	expires_in: z.number(),
	scope: z.literal('pagamento-pix.write boleto-cobranca.read boleto-cobranca.write'),
	token_type: z.literal('Bearer'),
})

export const bancoInterGetToken = action

	.input(z.object({ customerId: z.string().uuid() }))

	.output(schema)

	.handler(async ({ input: { customerId } }) => {
		// check for an existing token in Vercel KV Cache
		const cacheKey = `BANCO_INTER_TOKEN_CLIENT_ID=${customerId}`
		const cached = await kv.get(cacheKey)
		if (cached) return schema.parse(cached)

		const { db } = await import('@/src/db/db')

		const secrets = await db.secrets.select('description', 'data').where({
			id: customerId,
			description: {
				in: [
					'banking-banco-inter-client-id',
					'banking-banco-inter-client-secret',
					'banking-banco-inter-certificate',
					'banking-banco-inter-certificate-secret',
				],
			},
		})

		const clientId = secrets.find(s => s.description === 'banking-banco-inter-client-id')?.data
		const clientSecret = secrets.find(s => s.description === 'banking-banco-inter-client-secret')?.data
		const certificate = secrets.find(s => s.description === 'banking-banco-inter-certificate')?.data
		const certificateSecret = secrets.find(s => s.description === 'banking-banco-inter-certificate-secret')?.data

		if (!clientId || !clientSecret || !certificate || !certificateSecret) {
			throw new Error('Missing secrets for Banco Inter integration')
		}

		const certificateString = Buffer.from(certificate, 'base64').toString('utf-8')
		const certificateSecretString = Buffer.from(certificateSecret, 'base64').toString('utf-8')

		const headers = { 'Content-Type': 'application/x-www-form-urlencoded' }

		const body = new URLSearchParams()
		body.append('client_id', clientId)
		body.append('client_secret', clientSecret)
		body.append('grant_type', 'client_credentials')
		body.append('scope', 'pagamento-pix.write boleto-cobranca.read boleto-cobranca.write')

		const [agent, error] = await getHttpsAgentSignedWithCertAndKey({
			certificate: certificateString,
			secret: certificateSecretString,
		})
		if (!agent) throw new Error(`Error getting HTTPS agent with signed certificate: ${error.message}`)

		const req = await fetch('https://cdpj.partners.bancointer.com.br/oauth/v2/token', {
			agent: agent,
			body,
			headers,
			method: 'POST',
		})

		const urlencoded = new URLSearchParams()
		urlencoded.append('grant_type', 'client_credentials')

		const res = await req.json()

		const data = schema.parse(res)

		// add data to Vercel KV Cache
		await kv.set(cacheKey, data, { ex: data.expires_in })

		return data
	})
