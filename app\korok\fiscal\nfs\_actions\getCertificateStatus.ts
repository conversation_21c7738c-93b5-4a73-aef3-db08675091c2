'use server'

import { action } from '@/lib/actions/action'
import { db } from '@/src/db/db'
import { z } from 'zod'

export const getCertificateStatus = action

	.input(z.object({ customerId: z.string().uuid() }))

	.output(z.object({ isValid: z.boolean() }))

	.handler(async ({ input: { customerId } }) => {
		const [secret] = await db.secrets.select('expiration').where({
			id: customerId,
			description: 'icp-brasil-certificate',
		})

		// Check if certificate exists and is not expired
		const isValid = secret?.expiration ? new Date(secret.expiration) > new Date() : false

		return { isValid }
	})
