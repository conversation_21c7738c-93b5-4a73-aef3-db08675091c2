import db from '@/db/db'
import { customerSecrets } from '@/db/schema/customersSecrets'
import { DefaultAzureCredential } from '@azure/identity'
import { SecretClient } from '@azure/keyvault-secrets'

const KEY_VAULT_NAME = process.env.KEY_VAULT_NAME
const AZURE_VAULT_URL = `https://${KEY_VAULT_NAME}.vault.azure.net`
const SUPABASE_URL = process.env.SUPABASE_URL || ''
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || ''

interface MigrationResult {
	success: boolean
	message: string
	migratedCount: number
	errors: string[]
}

interface SecretToMigrate {
	customerId: string
	type: string
	expiration?: Date
}

async function getCustomerSecrets(limit?: number): Promise<SecretToMigrate[]> {
	const secrets = await db
		.select()
		.from(customerSecrets)
		.limit(limit || 1000)
	console.log(`Found ${secrets.length} secrets to migrate`)

	return secrets.map(secret => ({
		customerId: secret.customerId,
		type: secret.type,
		expiration: secret.expiration || undefined,
	}))
}

async function showMigrationStatus(): Promise<void> {
	try {
		const { createClient } = await import('@supabase/supabase-js')
		const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

		const totalOrigin = await db.select().from(customerSecrets)
		const { data: migratedData } = await supabase.rpc('sql', 'SELECT COUNT(*) as count FROM vault.secrets')
		const totalMigrated = migratedData?.[0]?.count || 0
		const pending = totalOrigin.length - totalMigrated

		console.log(`Origin: ${totalOrigin.length}, Migrated: ${totalMigrated}, Pending: ${pending}`)
	} catch (error) {
		console.log('Error checking status:', error)
	}
}

async function migrateSecretToSupabase(secret: SecretToMigrate): Promise<{ success: boolean; error?: string }> {
	try {
		if (!KEY_VAULT_NAME || !SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
			return { success: false, error: 'Environment variables not configured' }
		}

		const credential = new DefaultAzureCredential()
		const azureClient = new SecretClient(AZURE_VAULT_URL, credential)
		const secretName = `${secret.customerId}-${secret.type}`
		const azureSecret = await azureClient.getSecret(secretName)

		if (!azureSecret.value) {
			return { success: false, error: `Secret ${secretName} not found in Azure` }
		}

		const cleanValue = azureSecret.value.replaceAll('data:application/x-pkcs12;base64,', '')
		const { createClient } = await import('@supabase/supabase-js')
		const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

		const { error: createError } = await supabase.rpc('vault_create_secret', {
			secret_id: secret.customerId,
			secret: cleanValue,
			name: secretName,
			description: secret.type,
		})

		if (createError) {
			if (createError.message.includes('duplicate key') || createError.message.includes('already exists')) {
				const { error: updateError } = await supabase.rpc('vault_update_secret', {
					secret_id: secret.customerId,
					secret: cleanValue,
					name: secretName,
					description: secret.type,
				})
				if (updateError) return { success: false, error: `Update failed: ${updateError.message}` }
				console.log(`Updated: ${secretName}`)
			} else {
				return { success: false, error: `Create failed: ${createError.message}` }
			}
		} else {
			console.log(`Created: ${secretName}`)
		}

		return { success: true }
	} catch (error) {
		return { success: false, error: `Unexpected error: ${error}` }
	}
}

async function migrateSecrets(dryRun = true, limit?: number): Promise<MigrationResult> {
	console.log(`Starting migration (${dryRun ? 'DRY RUN' : 'EXECUTE'})`)
	if (limit) console.log(`Limited to ${limit} secrets`)

	const errors: string[] = []
	let migratedCount = 0

	try {
		const secrets = await getCustomerSecrets(limit)

		if (secrets.length === 0) {
			return { success: true, message: 'No secrets found', migratedCount: 0, errors: [] }
		}

		if (dryRun) {
			console.log('Preview - would migrate:')
			for (const secret of secrets) {
				console.log(`  ${secret.customerId}-${secret.type}`)
			}
			return { success: true, message: `DRY RUN: ${secrets.length} secrets would be migrated`, migratedCount: 0, errors: [] }
		}

		for (const secret of secrets) {
			const result = await migrateSecretToSupabase(secret)
			if (result.success) {
				migratedCount++
			} else {
				errors.push(`${secret.customerId}-${secret.type}: ${result.error}`)
			}
		}

		const message = `Migration completed: ${migratedCount}/${secrets.length} secrets migrated`
		console.log(message)

		if (errors.length > 0) {
			console.log('Errors:')
			for (const error of errors) {
				console.log(`  ${error}`)
			}
		}

		return { success: errors.length === 0, message, migratedCount, errors }
	} catch (error) {
		const errorMessage = `Fatal error: ${error}`
		console.error(errorMessage)
		return { success: false, message: errorMessage, migratedCount, errors: [errorMessage] }
	}
}

async function main() {
	const args = process.argv.slice(2)
	const dryRun = !args.includes('--execute')
	const testOne = args.includes('--test-one')
	const showStatus = args.includes('--status')

	if (showStatus) {
		await showMigrationStatus()
		return
	}

	if (dryRun) {
		console.log('DRY RUN mode. Use --execute to run migration.')
	}

	await showMigrationStatus()
	const result = await migrateSecrets(dryRun, testOne ? 1 : undefined)

	console.log(`\nResult: ${result.success ? 'Success' : 'Failed'}`)
	console.log(`Message: ${result.message}`)
	console.log(`Migrated: ${result.migratedCount}`)

	if (result.errors.length > 0) {
		console.log(`Errors: ${result.errors.length}`)
		for (const error of result.errors) {
			console.log(`  ${error}`)
		}
	}

	if (!dryRun) {
		console.log('\nFinal status:')
		await showMigrationStatus()
	}

	process.exit(result.success ? 0 : 1)
}

// Executar se chamado diretamente
if (require.main === module) {
	main().catch(console.error)
}

export { migrateSecrets }
