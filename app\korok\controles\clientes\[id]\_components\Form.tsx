'use client'

import { saveCustomer } from '@/app/korok/controles/clientes/[id]/_actions/saveCustomer'
import { showError } from '@/components/feedback/notifications/showError'
import useInputForm from '@/components/hooks/useInputForm'
import { regimeEspecialTributacaoMapping, stupidBooleanMapping } from '@/db/schema/customersNfseSettings'
import { customersPartnersQualificationMapping } from '@/db/schema/customersPartners'
import { customersTaxNumbersTypeMapping } from '@/db/schema/customersTaxNumbers'
import { taxPasswordTypesMapping } from '@/db/schema/customersTaxPasswords'
import { taxTimelineMapping } from '@/db/schema/customersTaxTimeline'
import { getCep } from '@/helpers/brasilApi/getCep'
import { testIds } from '@/playwright/testIds'
import type { CustomersInputSchema } from '@/src/db/tables/customers.schemas'
import { customerSecretsTypesValuesMapping } from '@/src/db/tables/customersSecrets.helpers'
import { SecretsInputSchema } from '@/src/db/tables/secrets.schemas'
import {
	Button,
	Center,
	Fieldset,
	FileInput,
	Group,
	Image,
	Loader,
	NumberInput,
	PasswordInput,
	SegmentedControl,
	Select,
	Stack,
	Switch,
	Tabs,
	Text,
	TextInput,
	rem,
} from '@mantine/core'
import { DatePickerInput } from '@mantine/dates'
import { Dropzone } from '@mantine/dropzone'
import { IconMinus, IconStar, IconUpload, IconX } from '@tabler/icons-react'
import { isCEP, isCNPJ } from 'brazilian-values'
import { useState, useTransition } from 'react'
import { init } from 'zod-empty'
import { type Dezerialize, type Zerialize, dezerialize } from 'zodex'
import type { inferServerActionReturnData } from 'zsa'
import { useServerAction } from 'zsa-react'
import { getCityById } from '../_actions/getCityById'
import { getCnpjData } from '../_actions/getCnpjData'
import { getPartnersData } from '../_actions/getPartnersData'
import type { getCustomer } from '../_data/getCustomer'
import type { getStates } from '../_data/getStates'
import { calculateCertificateExpiration } from '../_helpers/calculateCertificateExpiration'
import { AddItemToListButton } from './AddItemToListButton'
import { DeleteItemFromListButton } from './DeleteItemFromListButton'
import { DownloadCertificateButton } from './DownloadCertificateButton'
import { NothingHere } from './NothingHere'

const TABS = {
	dadosGerais: { label: 'Dados Gerais', value: 'dados_gerais' },
	quadroSocietario: { label: 'Quadro Societário', value: 'quadro_societario' },
	senhas: { label: 'Senhas', value: 'senhas' },
	configuracoesDeTributacao: { label: 'Configurações de Tributação', value: 'configuracoes_de_tributacao' },
	configuracoesDeFolha: { label: 'Configurações de Folha', value: 'configuracoes_de_folha' },
	demaisConfiguracoes: { label: 'Demais Configurações', value: 'demais_configuracoes' },
	certificadosDigitaisEChavesDeApi: { label: 'Certificados Digitais e Chaves de API', value: 'certificados_digitais_e_chaves_de_api' },
	integracoes: { label: 'Integrações', value: 'integracoes' },
} as const

type Props = {
	data: inferServerActionReturnData<typeof getCustomer>
	states: inferServerActionReturnData<typeof getStates>
	serializedCustomersInputSchema: Zerialize<typeof CustomersInputSchema>
}

export function Form({ data, states, serializedCustomersInputSchema }: Props) {
	const CustomersInputSchema = dezerialize(serializedCustomersInputSchema) as Dezerialize<typeof serializedCustomersInputSchema>

	const { SaveButton, form } = useInputForm({
		handleSubmit: saveCustomer,
		onSuccessGoTo: '/korok/controles/clientes',
		schema: CustomersInputSchema,
		initialValues: data,
		successMessage: 'Cliente salvo com sucesso',
	})

	const [cityTitle, setCityTitle] = useState<string | undefined>()

	const [isPendingLogo, startTransitionLogo] = useTransition()
	const [isPendingCertificate, startTransitionCertificate] = useTransition()

	const { execute: executeGetCnpjData, isPending: isPendingGetCnpjData } = useServerAction(getCnpjData)
	const { execute: executeGetPartnersData, isPending: isPendingGetPartnersData } = useServerAction(getPartnersData)
	const { execute: executeGetCep, isPending: isPendingGetCep } = useServerAction(getCep)
	const { execute: executeGetCityById, isPending: isPendingGetCityById } = useServerAction(getCityById)

	const { values, getInputProps, setFieldValue, insertListItem, removeListItem, setValues } = form

	const cnpjValue = values.taxNumbers.find(({ type }) => type === 'CNPJ')?.taxNumber
	const hasValidCnpj = cnpjValue && isCNPJ(cnpjValue)

	form.watch('cep', async ({ value: cep }) => {
		if (!cep || !isCEP(cep)) return

		const [data] = await executeGetCep({ cep })
		if (!data) return

		const { cep: _, ...rest } = data // removes `cep` from the data to avoid setting it again and causing a re-render

		setValues({
			...rest,
			cityId: data.cityId ?? undefined,
			logradouro: data.street,
			bairro: data.neighborhood,
		})

		setCityTitle(data.city)
	})

	return (
		<form>
			<Tabs defaultValue={TABS.dadosGerais.value} style={{ flex: 1 }}>
				<Tabs.List>
					{Object.values(TABS).map(({ value, label }) => (
						<Tabs.Tab key={value} value={value}>
							{label}
						</Tabs.Tab>
					))}
				</Tabs.List>

				<Tabs.Panel value={TABS.dadosGerais.value}>
					<Fieldset legend="Inscrições">
						{values.taxNumbers.length ? (
							values.taxNumbers.map(({ type }, i) => (
								<Group justify="center" key={`inscricoes-${type}-${i.toString()}`} mt="xs">
									<Select
										data={customersTaxNumbersTypeMapping}
										label="Tipo"
										placeholder="Escolha uma..."
										{...getInputProps(`taxNumbers.${i}.type`)}
									/>
									{type === 'IE' ? (
										<Select
											data={states}
											label="Estado"
											placeholder="Escolha uma..."
											searchable
											{...getInputProps(`taxNumbers.${i}.stateId`)}
										/>
									) : type === 'Cartório' ? (
										<TextInput
											label="Cartório"
											{...getInputProps(`taxNumbers.${i}.details`)}
											value={values.taxNumbers[i]?.details ?? ''} // Avoids `value` prop on `input` should not be null
										/>
									) : null}
									<TextInput label="Inscrição" {...getInputProps(`taxNumbers.${i}.taxNumber`)} />
									<DeleteItemFromListButton onClick={() => removeListItem('taxNumbers', i)} />
								</Group>
							))
						) : (
							<NothingHere />
						)}
						<AddItemToListButton onClick={() => insertListItem('taxNumbers', init(CustomersInputSchema.shape.taxNumbers.element))} />
					</Fieldset>

					<Fieldset legend="Nome e endereço">
						<Stack gap="md">
							<Group align="flex-end">
								<TextInput flex={1} data-testid={testIds['title-input']} label="Título" {...getInputProps('title')} />
								<Button
									loading={isPendingGetCnpjData}
									disabled={!hasValidCnpj}
									onClick={async () => {
										if (!cnpjValue) return
										const [data, error] = await executeGetCnpjData({ cnpj: cnpjValue })
										if (!data) return showError(error?.message)

										const { razaoSocial, logradouro, tipoLogradouro, numero, complemento, bairro, cityId, cep } = data

										setValues({
											...values,
											title: razaoSocial,
											tipoLogradouro: tipoLogradouro ?? values.tipoLogradouro,
											logradouro: logradouro ?? values.logradouro,
											numero: numero ?? values.numero,
											complemento: complemento ?? values.complemento,
											bairro: bairro ?? values.bairro,
											cep: cep ?? values.cep,
										})

										if (cityId) {
											const [data] = await executeGetCityById({ id: cityId })
											if (!data) return
											setCityTitle(data)
										}
									}}
								>
									Atualizar pelo CNPJ
								</Button>
							</Group>

							<Group>
								<TextInput flex={0.5} label="CEP" {...getInputProps('cep')} rightSection={isPendingGetCep ? <Loader size="sm" /> : null} />
								<TextInput flex={0.5} label="Tipo de Logradouro" {...getInputProps('tipoLogradouro')} />
								<TextInput flex={1} label="Logradouro" {...getInputProps('logradouro')} />
								<TextInput flex={0.5} label="Número" {...getInputProps('numero')} />
							</Group>

							<Group>
								<TextInput flex={0.5} label="Complemento" {...getInputProps('complemento')} />
								<TextInput flex={1} label="Bairro" {...getInputProps('bairro')} />
								<TextInput
									flex={1}
									label="Município"
									disabled
									value={cityTitle}
									rightSection={isPendingGetCityById ? <Loader data-testid={testIds['loader-city']} size="sm" /> : null}
								/>
								<TextInput flex={1} label="Inscrição Municipal" {...getInputProps('inscricaoMunicipal')} />
								<SegmentedControl
									flex={0.5}
									color="blue"
									data={[
										{ label: 'Matriz', value: 'headquarters' },
										{ label: 'Filial', value: 'branch' },
									]}
									{...getInputProps('unit')}
								/>
							</Group>
						</Stack>
					</Fieldset>

					<Fieldset legend="Contrato">
						<Stack>
							<Group>
								<DatePickerInput flex={1} label="Início do contrato" {...getInputProps('from')} />
								<DatePickerInput flex={1} label="Fim do contrato" {...getInputProps('until')} clearable />
							</Group>
							<TextInput label="E-mail para envio de boletos e notas fiscais" {...getInputProps('emailForInvoices')} />
						</Stack>
					</Fieldset>
				</Tabs.Panel>

				<Tabs.Panel value={TABS.quadroSocietario.value}>
					{values.partners.length ? (
						values.partners.map((_, i) => (
							<Group justify="center" key={`quadro-societario-${i.toString()}`} mt="xs">
								<Select
									data={customersPartnersQualificationMapping}
									label="Qualificação"
									placeholder="Escolha uma..."
									{...getInputProps(`partners.${i}.rfbQualification`)}
								/>
								<TextInput label="Nome" {...getInputProps(`partners.${i}.name`)} />
								<TextInput label="CPF / CNPJ" {...getInputProps(`partners.${i}.taxNumber`)} />
								<Switch
									label="É responsável perante a RFB?"
									{...getInputProps(`partners.${i}.isResponsibleForRfb`, { type: 'checkbox' })}
								/>
								<DeleteItemFromListButton onClick={() => removeListItem('partners', i)} />
							</Group>
						))
					) : (
						<NothingHere />
					)}
					<Group align="flex-end">
						<AddItemToListButton onClick={() => insertListItem('partners', init(CustomersInputSchema.shape.partners.element))} />
						<Button
							loading={isPendingGetPartnersData}
							disabled={!hasValidCnpj}
							onClick={async () => {
								if (!cnpjValue) return
								const [data, error] = await executeGetPartnersData({ cnpj: cnpjValue })
								if (!data) return showError(error.message)

								for (const { taxNumber, name, rfbQualification } of data) {
									const adjustedTaxNumber = taxNumber?.substring(3, 9) // removes the '*' from the tax number
									const existing = values.partners.findIndex(({ taxNumber }) => taxNumber?.substring(3, 9) === adjustedTaxNumber)

									// If exists, update it's name and RFB Qualification
									if (existing !== -1 && rfbQualification) {
										setFieldValue(`partners.${existing}.name`, name)
										setFieldValue(`partners.${existing}.rfbQualification`, rfbQualification)

										continue
									}

									insertListItem('partners', { ...init(CustomersInputSchema.shape.partners.element), taxNumber, name, rfbQualification })
								}

								// Remove partners that are not in the response
								const notInQsa = values.partners.filter(
									({ taxNumber }) => !data.some(e => e.taxNumber?.substring(3, 9) === taxNumber?.substring(3, 9)),
								)

								for (const { taxNumber } of notInQsa) {
									removeListItem(
										'partners',
										values.partners.findIndex(({ taxNumber: t }) => t?.substring(3, 9) === taxNumber?.substring(3, 9)),
									)
								}
							}}
						>
							Atualizar pelo CNPJ
						</Button>
					</Group>
				</Tabs.Panel>

				<Tabs.Panel value={TABS.senhas.value}>
					{values.taxPasswords.length ? (
						values.taxPasswords.map(({ type }, i) => (
							<Group justify="center" key={`inscricoes-${type}-${i.toString()}`} mt="xs">
								<Select
									data={taxPasswordTypesMapping}
									label="Tipo"
									placeholder="Escolha uma..."
									{...getInputProps(`taxPasswords.${i}.type`)}
								/>
								<TextInput label="Login" {...getInputProps(`taxPasswords.${i}.login`)} />
								<TextInput label="Senha" {...getInputProps(`taxPasswords.${i}.password`)} />
								<DeleteItemFromListButton onClick={() => removeListItem('taxPasswords', i)} />
							</Group>
						))
					) : (
						<NothingHere />
					)}
					<AddItemToListButton onClick={() => insertListItem('taxPasswords', init(CustomersInputSchema.shape.taxPasswords.element))} />
				</Tabs.Panel>

				<Tabs.Panel value={TABS.configuracoesDeTributacao.value}>
					<Fieldset legend="Tributação">
						{values.taxTimelines.length ? (
							values.taxTimelines.map(({ tax }, i) => (
								<Group justify="center" key={`configuracoesDeTributacao-${tax}-${i.toString()}`} mt="xs">
									<DatePickerInput label="De" {...getInputProps(`taxTimelines.${i}.from`)} />
									<DatePickerInput label="Até" {...getInputProps(`taxTimelines.${i}.until`)} />
									<Select
										data={taxTimelineMapping}
										label="Tributação"
										placeholder="Escolha uma..."
										{...getInputProps(`taxTimelines.${i}.tax`)}
									/>
									<DeleteItemFromListButton onClick={() => removeListItem('taxTimelines', i)} />
								</Group>
							))
						) : (
							<NothingHere />
						)}
						<AddItemToListButton onClick={() => insertListItem('taxTimelines', init(CustomersInputSchema.shape.taxTimelines.element))} />
					</Fieldset>
					<Fieldset legend="Regras Tributárias">
						<Stack>
							{values.taxesConfigs.length ? (
								values.taxesConfigs.map(({ from, until, irrfRent }, i) => (
									<Group justify="center" key={`taxesConfigs-${from}-${until}-${i.toString()}`} mt="xs">
										<DatePickerInput label="De" {...getInputProps(`taxesConfigs.${i}.from`)} />
										<DatePickerInput label="Até" {...getInputProps(`taxesConfigs.${i}.until`)} />
										<Stack>
											<Switch
												label="Tem DAPI?" //
												{...getInputProps(`taxesConfigs.${i}.dapi`, { type: 'checkbox' })}
												checked={values.taxesConfigs[i]?.dapi}
											/>
											<Switch
												label="É contribuinte do IPI?"
												{...getInputProps(`taxesConfigs.${i}.ipiTaxPayer`, { type: 'checkbox' })}
												checked={values.taxesConfigs[i]?.ipiTaxPayer}
											/>
											<Switch
												label="É substituto tributário do ISSQN?"
												{...getInputProps(`taxesConfigs.${i}.issqnSubstitute`, { type: 'checkbox' })}
												checked={values.taxesConfigs[i]?.issqnSubstitute}
											/>
											<Switch
												label="Está sujeito à apuração da Contribuição Previdenciária sobre a Receita Bruta (CPRB)?"
												{...getInputProps(`taxesConfigs.${i}.cprb`, { type: 'checkbox' })}
												checked={values.taxesConfigs[i]?.cprb}
											/>
											<Switch
												label="Teve movimentação no período?"
												{...getInputProps(`taxesConfigs.${i}.hasActivities`, { type: 'checkbox' })}
												checked={values.taxesConfigs[i]?.hasActivities}
											/>
											<Switch
												label="Tem regime especial de tributação (RET) do ICMS?"
												{...getInputProps(`taxesConfigs.${i}.icmsSpecialRegim`, { type: 'checkbox' })}
												checked={values.taxesConfigs[i]?.icmsSpecialRegim}
											/>
											<Switch
												label="Prefere envio de guias da DCTFWeb unificadas?"
												{...getInputProps(`taxesConfigs.${i}.unifiedDctfwebTaxes`, { type: 'checkbox' })}
												checked={values.taxesConfigs[i]?.unifiedDctfwebTaxes}
											/>
											<Switch
												label="Tem contrato de aluguel com retenção de imposto de renda de pessoa física?"
												{...getInputProps(`taxesConfigs.${i}.irrfRent`, { type: 'checkbox' })}
												checked={irrfRent}
											/>
										</Stack>
										<DeleteItemFromListButton onClick={() => removeListItem('taxesConfigs', i)} />
									</Group>
								))
							) : (
								<NothingHere />
							)}
							<AddItemToListButton
								onClick={() => {
									insertListItem('taxesConfigs', init(CustomersInputSchema.shape.taxesConfigs.element))
								}}
							/>
						</Stack>
					</Fieldset>
					<Fieldset legend="Configurações para emissão de notas fiscais">
						<Stack>
							{values.nfseSettings ? (
								values.nfseSettings.map((_, i) => (
									<Group justify="center" key={`nfseSettings-${i.toString()}`} mt="xs">
										<NumberInput
											decimalScale={2}
											decimalSeparator=","
											label="Aliquota"
											min={0}
											max={5}
											{...getInputProps(`nfseSettings.${i}.aliquota`)}
										/>
										<TextInput
											label="Código de Tributação no Município"
											{...getInputProps(`nfseSettings.${i}.codigoTributacaoMunicipio`)}
										/>
										<TextInput label="Descrição do Serviço" {...getInputProps(`nfseSettings.${i}.descricaoServico`)} />
										<Select data={stupidBooleanMapping} label="ISS Retido" {...getInputProps(`nfseSettings.${i}.issRetido`)} />
										<TextInput label="Item da Lista de Serviços" {...getInputProps(`nfseSettings.${i}.itemListaServico`)} />
										<Select
											data={stupidBooleanMapping}
											label="Optante pelo Simples Nacional"
											{...getInputProps(`nfseSettings.${i}.optanteSimplesNacional`)}
										/>
										<Select
											data={regimeEspecialTributacaoMapping}
											label="Regime Especial de Tributação"
											{...getInputProps(`nfseSettings.${i}.regimeEspecialTributacao`)}
										/>
									</Group>
								))
							) : (
								<NothingHere />
							)}

							<AddItemToListButton
								onClick={() => {
									insertListItem('nfseSettings', init(CustomersInputSchema.shape.nfseSettings.element))
								}}
							/>
						</Stack>
					</Fieldset>
				</Tabs.Panel>

				<Tabs.Panel value={TABS.configuracoesDeFolha.value}>
					<Stack>
						{values.payrollConfigs.length ? (
							values.payrollConfigs.map(({ from, until }, i) => (
								<Group justify="center" key={`payrollConfigs-${from}-${until}`} mt="xs">
									<DatePickerInput label="De" {...getInputProps(`payrollConfigs.${i}.from`)} />
									<DatePickerInput label="Até" {...getInputProps(`payrollConfigs.${i}.until`)} />
									<Stack>
										<Switch
											label="Tem empregados e/ou movimento de folha?"
											{...getInputProps(`payrollConfigs.${i}.hasActivities`, { type: 'checkbox' })}
											checked={values.payrollConfigs[i]?.hasActivities}
										/>
										<Switch
											label="Tem adiantamento"
											{...getInputProps(`payrollConfigs.${i}.advance`, { type: 'checkbox' })}
											checked={values.payrollConfigs[i]?.advance}
										/>
										<Switch
											label="Tem FGTS"
											{...getInputProps(`payrollConfigs.${i}.fgts`, { type: 'checkbox' })}
											checked={values.payrollConfigs[i]?.fgts}
										/>
									</Stack>

									<DeleteItemFromListButton onClick={() => removeListItem('payrollConfigs', i)} />
								</Group>
							))
						) : (
							<NothingHere />
						)}
						<AddItemToListButton
							onClick={() => insertListItem('payrollConfigs', init(CustomersInputSchema.shape.payrollConfigs.element))}
						/>
					</Stack>
				</Tabs.Panel>
				<Tabs.Panel value={TABS.demaisConfiguracoes.value}>
					<Fieldset legend="Logomarca">
						<Stack>
							<Dropzone
								accept={['image/png', 'image/x-png']}
								disabled={isPendingLogo}
								maxSize={2 * 1024 ** 2}
								onDrop={files => {
									startTransitionLogo(async () => {
										await Promise.all(
											files.map(async e => {
												const fileBuffer = await e.arrayBuffer()
												const base64 = Buffer.from(fileBuffer).toString('base64')
												setFieldValue('logo', `data:image/png;base64,${base64}`)
											}),
										)
									})
								}}
							>
								<Group gap="xl" style={{ minHeight: 100, pointerEvents: 'none' }}>
									<Dropzone.Accept>
										<IconUpload size={50} />
									</Dropzone.Accept>
									<Dropzone.Reject>
										<IconX size={50} />
									</Dropzone.Reject>
									<Dropzone.Idle>
										{values.logo ? (
											<Stack align="center">
												<Center>
													<Image alt="Logo" mah={150} src={values.logo} />
												</Center>
												<Text c="dimmed" inline mt={7} size="sm">
													Clique aqui para atualizar a logomarca do cliente.
												</Text>
											</Stack>
										) : (
											<>
												<Text inline size="xl">
													Insira ou atualize a logo do cliente
												</Text>
												<Text c="dimmed" inline mt={7} size="sm">
													São permitidos arquivos no formato PNG. O tamanho máximo do arquivo é de 2 MB.
												</Text>
											</>
										)}
									</Dropzone.Idle>
								</Group>
							</Dropzone>
							<Switch
								disabled={!values.logo?.length}
								label="Mostrar em 'Clientes que nos orgulham'"
								{...getInputProps('showcaseOnLandingPage')}
								checked={values.showcaseOnLandingPage ?? false}
								offLabel={<IconMinus color="grey" stroke={2.5} style={{ height: rem(16), width: rem(16) }} />}
								onLabel={<IconStar color="yellow" stroke={2.5} style={{ height: rem(16), width: rem(16) }} />}
								size="md"
							/>
						</Stack>
					</Fieldset>
				</Tabs.Panel>
				<Tabs.Panel value={TABS.certificadosDigitaisEChavesDeApi.value}>
					<Fieldset legend="Certificados Digitais e Chaves de API">
						<Stack justify="center">
							{values.secrets.length ? (
								values.secrets.map((secret, i) => {
									const type = (() => {
										try {
											const metadata = JSON.parse(secret.description || '{}')
											return metadata.type || secret.description || ''
										} catch {
											return secret.description || ''
										}
									})()
									const expiration = (() => {
										try {
											const metadata = JSON.parse(secret.description || '{}')
											return metadata.expiration ? new Date(metadata.expiration) : null
										} catch {
											return null
										}
									})()
									const data = secret.data

									return (
										<Group key={`secrets-${type}-${expiration}-${i.toString()}`}>
											<Select
												data={customerSecretsTypesValuesMapping.filter(
													option =>
														!values.secrets
															.filter((_, index) => index !== i)
															.map(s => {
																try {
																	const metadata = JSON.parse(s.description || '{}')
																	return metadata.type || s.description || ''
																} catch {
																	return s.description || ''
																}
															})
															.includes(option.value),
												)}
												label="Tipo"
												{...getInputProps(`secrets.${i}.description`)}
											/>

											{type === 'icp-brasil-certificate' && (
												<>
													<FileInput
														disabled={isPendingCertificate}
														label="Certificado digital"
														accept="application/x-pkcs12,.pfx"
														onChange={f => {
															if (!f) return
															startTransitionCertificate(async () => {
																const certBuffer = Buffer.from(await f.arrayBuffer())
																const certBase64 = certBuffer.toString('base64')

																setFieldValue(`secrets.${i}.data`, certBase64)

																const expiration = calculateCertificateExpiration({
																	certificateData: certBase64,
																	passphraseData: values.secrets.find(e => {
																		try {
																			const metadata = JSON.parse(e.description || '{}')
																			return metadata.type === 'icp-brasil-secret'
																		} catch {
																			return e.description === 'icp-brasil-secret'
																		}
																	})?.data,
																})

																// Update description with expiration metadata
																const metadata = { type: 'icp-brasil-certificate', expiration: expiration?.toISOString() }
																setFieldValue(`secrets.${i}.description`, JSON.stringify(metadata))
															})
														}}
														placeholder={data ? `${values.title}.p12` : 'Clique aqui para enviar um arquivo'}
													/>
													<DatePickerInput
														disabled //
														label="Validade do certificado"
														value={expiration}
													/>
												</>
											)}

											{type === 'icp-brasil-secret' && (
												<PasswordInput
													disabled={
														!values.secrets.find(e => {
															try {
																const metadata = JSON.parse(e.description || '{}')
																return metadata.type === 'icp-brasil-certificate'
															} catch {
																return e.description === 'icp-brasil-certificate'
															}
														})
													}
													label="Senha do certificado"
													placeholder="Shhh... É segredo"
													{...getInputProps(`secrets.${i}.data`)}
													onChange={e => {
														if (!e.target.value) return
														setFieldValue(`secrets.${i}.data`, e.target.value)

														const expiration = calculateCertificateExpiration({
															certificateData: values.secrets.find(e => {
																try {
																	const metadata = JSON.parse(e.description || '{}')
																	return metadata.type === 'icp-brasil-certificate'
																} catch {
																	return e.description === 'icp-brasil-certificate'
																}
															})?.data,
															passphraseData: e.target.value,
														})

														const certificateIndex = values.secrets.findIndex(e => {
															try {
																const metadata = JSON.parse(e.description || '{}')
																return metadata.type === 'icp-brasil-certificate'
															} catch {
																return e.description === 'icp-brasil-certificate'
															}
														})

														if (certificateIndex !== -1) {
															const metadata = { type: 'icp-brasil-certificate', expiration: expiration?.toISOString() }
															setFieldValue(`secrets.${certificateIndex}.description`, JSON.stringify(metadata))
														}
													}}
												/>
											)}

											{type === 'banking-banco-inter-certificate' && (
												<FileInput
													label="Certificado digital"
													accept="application/x-pkcs12,.pfx"
													onChange={f => {
														if (!f) return
														startTransitionCertificate(async () => {
															const certBuffer = Buffer.from(await f.arrayBuffer())
															const certBase64 = certBuffer.toString('base64')
															setFieldValue(`secrets.${i}.data`, certBase64)
														})
													}}
													placeholder={typeof data !== 'string' ? `${values.title}-Inter.key` : 'Clique aqui para enviar um arquivo'}
												/>
											)}

											{type === 'banking-banco-inter-certificate-secret' && (
												<FileInput
													label="Certificado digital"
													accept="application/x-pkcs12,.pfx"
													onChange={f => {
														if (!f) return
														startTransitionCertificate(async () => {
															const certBuffer = Buffer.from(await f.arrayBuffer())
															const certBase64 = certBuffer.toString('base64')
															setFieldValue(`secrets.${i}.data`, certBase64)
														})
													}}
													placeholder={typeof data !== 'string' ? `${values.title}-Inter.key` : 'Clique aqui para enviar um arquivo'}
												/>
											)}

											{type === 'banking-banco-inter-client-id' && (
												<TextInput label="Client Id" placeholder="Shhh... É segredo" {...getInputProps(`secrets.${i}.data`)} />
											)}

											{type === 'banking-banco-inter-client-secret' && (
												<PasswordInput label="Client Secret" placeholder="Shhh... É segredo" {...getInputProps(`secrets.${i}.data`)} />
											)}

											<DownloadCertificateButton title={values.title} type={type} tempData={data} />
											<DeleteItemFromListButton onClick={() => removeListItem('secrets', i)} />
										</Group>
									)
								})
							) : (
								<NothingHere />
							)}
							<AddItemToListButton onClick={() => insertListItem('secrets', { ...init(SecretsInputSchema), tempData: '' })} />
						</Stack>
					</Fieldset>
				</Tabs.Panel>
				<Tabs.Panel value={TABS.integracoes.value}>
					<Fieldset legend="Integrações">
						<Stack>
							<TextInput label="Id do time do Rocket.Chat" {...getInputProps('rocketChatTeamId')} disabled />
							<TextInput label="Id do departamento do Rocket.Chat" {...getInputProps('rocketChatDepartmentId')} disabled />
							<TextInput label="Id do grupo do Seafile" {...getInputProps('seafileGroupId')} disabled />
							<TextInput label="Id da biblioteca do Seafile" {...getInputProps('seafileLibraryId')} disabled />
							<TextInput label="Id da API do Onvio" {...getInputProps('onvioApiId')} disabled />
							<TextInput label="Secret da API do Onvio" {...getInputProps('onvioApiSecret')} disabled />
							<TextInput label="Chave da API do Solides" {...getInputProps('solidesApiKey')} disabled />
							<TextInput label="Chave de acesso da Invoicy" {...getInputProps('invoicyAccessKey')} disabled />
						</Stack>
					</Fieldset>
				</Tabs.Panel>
			</Tabs>

			<SaveButton />
		</form>
	)
}
