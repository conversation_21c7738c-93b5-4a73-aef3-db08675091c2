import { getHttpsAgentSignedWithCertAndKey } from '@/helpers/certificate/getHttpsAgentSignedWithCertAndKey'
import { action } from '@/lib/actions/action'
import fetch from 'node-fetch'
import type { RequestInit } from 'node-fetch'
import { z } from 'zod'
import { bancoInterGetToken } from './bancoInterGetToken'

export const bancoInterMakeAuthorizedFetch = action

	.input(
		z.object({
			customerId: z.string().uuid(),
			url: z.string().url(),
			requestOptions: z.any().optional() as z.ZodType<RequestInit | undefined>,
		}),
	)

	.handler(async ({ input: { customerId, url, requestOptions } }) => {
		const { db } = await import('@/src/db/db')

		const secrets = await db.secrets.select('description', 'data').where({
			id: customerId,
			description: { in: ['banking-banco-inter-certificate', 'banking-banco-inter-certificate-secret'] },
		})

		const certificate = secrets.find(s => s.description === 'banking-banco-inter-certificate')?.data
		const certificateSecret = secrets.find(s => s.description === 'banking-banco-inter-certificate-secret')?.data

		if (!certificate || !certificateSecret) {
			throw new Error('Missing certificate secrets for Banco Inter')
		}

		const certificateString = Buffer.from(certificate, 'base64').toString('utf-8')
		const certificateSecretString = Buffer.from(certificateSecret, 'base64').toString('utf-8')

		const [token, tokenError] = await bancoInterGetToken({ customerId })
		if (!token) throw tokenError

		const [agent, error] = await getHttpsAgentSignedWithCertAndKey({
			certificate: certificateString,
			secret: certificateSecretString,
		})
		if (!agent) throw error

		return fetch(url, {
			...requestOptions,
			agent,
			headers: {
				...requestOptions?.headers,
				Authorization: token.access_token,
				'Content-Type': 'application/json',
			},
		})
	})
