import { dayjs } from '@/helpers/dayjs'

import type { db } from '@/src/db/db'
import { BaseTable, sql } from '../baseTable'
import { CitiesTable } from './cities.table'
import { CustomersNfseSettingsTable } from './customersNfseSettings.table'
import { CustomersPartnersTable } from './customersPartners.table'
import { CustomersPayrollConfigsTable } from './customersPayrollConfigs.table'
import { CustomersTaxNumbersTable } from './customersTaxNumbers.table'
import { CustomersTaxPasswordsTable } from './customersTaxPasswords.table'
import { CustomersTaxTimelineTable } from './customersTaxTimeline.table'
import { CustomersTaxesConfigsTable } from './customersTaxesConfigs.table'
import { PermissionsTable } from './permissions.table'
import { SecretsTable } from './secrets.table'
import { StatesTable } from './states.table'
import { UsersTable } from './users.table'

export class CustomersTable extends BaseTable {
	readonly table = 'customers'

	columns = this.setColumns(
		t => ({
			bairro: t.text().nullable(),
			cep: t.text().nullable(),
			from: t.timestampNoTZ().input(z => z.transform(v => dayjs.tz(v).startOf('day').toDate())),
			id: t.uuid().primaryKey().default(t.sql`gen_random_uuid()`),
			logo: t.text().nullable(),
			logradouro: t.text().nullable(),
			numero: t.text().nullable(),
			title: t.varchar(255).input(s => s.min(3, { message: 'O título deve conter pelo menos 3 caracteres' })),
			unit: t.enum('public.customers_unit_enum', ['headquarters', 'branch']),
			until: t
				.timestampNoTZ()
				.input(z => z.transform(v => dayjs.tz(v).endOf('day').toDate()))
				.nullable()
				.index('customers_until_index'),
			seafileGroupId: t.integer().nullable(),
			seafileLibraryId: t.text().nullable(),
			onvioApiId: t.text().nullable(),
			onvioApiSecret: t.text().nullable(),
			showcaseOnLandingPage: t.boolean().nullable(),
			solidesApiKey: t.text().nullable(),
			complemento: t.text().nullable(),
			inscricaoMunicipal: t
				.varchar()
				.nullable()
				.input(s => s.transform(e => (e ? e.replace(/[^\dX]/gi, '') : e))),
			cityId: t.uuid().foreignKey('cities', 'id', { name: 'customers_city_id_cities_id_fk', onUpdate: 'CASCADE', onDelete: 'SET NULL' }),
			tipoLogradouro: t.text().nullable(),
			invoicyAccessKey: t.text().nullable(),
			emailForInvoices: t.text().email().nullable(),
			rocketChatTeamId: t.text().nullable(),
			rocketChatDepartmentId: t.text().nullable(),
			isInvoicyActive: t.boolean().nullable().default(false),
			taxUserId: t
				.uuid()
				.nullable()
				.foreignKey('users', 'id', { name: 'customers_tax_user_id_users_id_fk', onUpdate: 'CASCADE', onDelete: 'SET NULL' }),
			accountingUserId: t
				.uuid()
				.nullable()
				.foreignKey('users', 'id', { name: 'customers_accounting_user_id_users_id_fk', onUpdate: 'CASCADE', onDelete: 'SET NULL' }),
			payrollUserId: t
				.uuid()
				.nullable()
				.foreignKey('users', 'id', { name: 'customers_payroll_user_id_users_id_fk', onUpdate: 'CASCADE', onDelete: 'SET NULL' }),
			lawUserId: t
				.uuid()
				.nullable()
				.foreignKey('users', 'id', { name: 'customers_lawyer_user_id_users_id_fk', onUpdate: 'CASCADE', onDelete: 'SET NULL' }),
		}),
		t => t.unique(['id', 'title'], 'customers_id_title_index'),
	)

	init(orm: typeof db) {
		this.afterSaveCommit(['id'], async () => {
			// Dynamic import to avoid client-side issues
			if (typeof window === 'undefined') {
				const { revalidateTag } = await import('next/cache')
				revalidateTag(orm.customers.table)
				revalidateTag(orm.customersNfseSettings.table)
				revalidateTag(orm.customersPartners.table)
				revalidateTag(orm.customersPayrollConfigs.table)
				revalidateTag(orm.customersTaxNumbers.table)
				revalidateTag(orm.customersTaxPasswords.table)
				revalidateTag(orm.customersTaxTimeline.table)
				revalidateTag(orm.secrets.table)
			}
		})
	}

	scopes = this.setScopes({
		active: q => q.where({ OR: [{ until: null }, { until: { gte: sql`now()` } }] }),
		activeSinceLastYear: q => q.where({ OR: [{ until: null }, { until: { gte: sql`now() - INTERVAL '1 year'` } }] }),
	})

	relations = {
		nfseSettings: this.hasMany(() => CustomersNfseSettingsTable, { columns: ['id'], references: ['customerId'] }),
		partners: this.hasMany(() => CustomersPartnersTable, { columns: ['id'], references: ['customerId'] }),
		payrollConfigs: this.hasMany(() => CustomersPayrollConfigsTable, { columns: ['id'], references: ['customerId'] }),
		taxesConfigs: this.hasMany(() => CustomersTaxesConfigsTable, { columns: ['id'], references: ['customerId'] }),
		taxNumbers: this.hasMany(() => CustomersTaxNumbersTable, { columns: ['id'], references: ['customerId'] }),
		taxPasswords: this.hasMany(() => CustomersTaxPasswordsTable, { columns: ['id'], references: ['customerId'] }),
		taxTimelines: this.hasMany(() => CustomersTaxTimelineTable, { columns: ['id'], references: ['customerId'] }),
		secrets: this.hasMany(() => SecretsTable, { columns: ['id'], references: ['id'] }),
		permissions: this.hasMany(() => PermissionsTable, { columns: ['id'], references: ['customerId'] }),
		users: this.hasMany(() => UsersTable, { through: 'permissions', source: 'user' }),
		city: this.hasOne(() => CitiesTable, { columns: ['cityId'], references: ['id'] }),
		state: this.hasOne(() => StatesTable, { through: 'city', source: 'state' }),
	}
}
